<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_781_8517" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="62" height="62">
<rect width="62" height="62" rx="31" fill="#C3C3C5"/>
</mask>
<g mask="url(#mask0_781_8517)">
</g>
<path d="M14 21C14 12.7157 20.7157 6 29 6H50C53.3137 6 56 8.68629 56 12V41C56 49.2843 49.2843 56 41 56H20C16.6863 56 14 53.3137 14 50V21Z" fill="#74A3FF"/>
<path d="M14 18C14 11.3726 19.3726 6 26 6H47V19.5C47 26.9558 40.9558 33 33.5 33H14V18Z" fill="#6297FF"/>
<path d="M10 53.5C12 53 13.6667 51.8333 14 50C15 52 22.5 56 15.5 56C11 56 10 53.5 10 53.5Z" fill="#74A3FF"/>
<path d="M22.5 43.5C22.5 43.5 22.5177 43.9856 22.4437 44.7513L21.9376 56L11.5057 55.0005C11.6645 55.0151 15.146 55.2955 18.8799 52.2105C21.5717 49.9865 22.2676 46.5747 22.4437 44.7513L22.5 43.5Z" fill="#6892FF"/>
<g filter="url(#filter0_d_781_8517)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M32.428 17.7641C32.428 15.4388 30.6645 14.0028 27.479 14.0028V14H21.657L21.4584 14.7591C23.2921 14.8893 23.3563 15.0763 23.0088 16.8549L21.3709 25.0403C21.0263 26.7113 20.7635 26.8812 19.1927 27.0059L19 27.7451H25.7593L25.9578 27.0059C23.9519 26.8359 23.9081 26.6235 24.1914 25.1451L24.8045 21.9502L26.2206 22.2675C26.4133 22.3099 26.7403 22.3524 26.9184 22.3524C27.3866 22.3524 27.8388 22.3134 28.2691 22.2371C29.0303 22.9053 29.5184 25.9794 29.5578 26.2567C28.1621 28.1799 27.5314 28.8369 27.1373 28.8369C26.9293 28.8369 26.7576 28.6576 26.5967 28.4897C26.4551 28.3417 26.3218 28.2025 26.1796 28.2025C25.7416 28.2025 25.0438 28.8738 25 29.681C25 30.423 25.546 30.9073 26.2234 30.9073C27.2044 30.9073 28.1417 29.7659 29.8643 27.1461L30.276 28.4971C30.8454 30.2927 31.3271 30.9073 32.0892 30.9073C32.8951 30.9073 34.2236 30.2729 35.4469 28.3073L35.0323 27.6927C34.3141 28.6245 33.771 28.9417 33.5082 28.9417C33.225 28.9417 32.9622 28.5594 32.6352 27.5879L31.7826 24.9879C32.6994 23.634 33.4411 22.9373 33.917 22.9373C34.1664 22.9373 34.3185 23.1103 34.461 23.2724C34.5821 23.4101 34.6962 23.54 34.8571 23.5491C35.0761 23.5491 35.2484 23.4669 35.4674 23.2743C35.7681 23.0081 36.1477 22.6229 36.101 22.1103C36.0484 21.5353 35.4761 21.0821 34.9418 21.0113C34.898 21.0057 34.8484 21 34.79 21C34.536 21 34.1272 21.102 33.4557 21.6882C32.9067 22.1669 32.2731 22.9345 31.4761 24.0759L31.1286 22.9996C30.8892 22.2822 30.6562 21.7718 30.3887 21.4479C30.534 21.3566 30.6728 21.2587 30.8046 21.1544C30.8134 21.1473 30.8229 21.1402 30.8324 21.1331C30.8419 21.126 30.8514 21.119 30.8601 21.1119C31.1462 20.8825 31.4003 20.6191 31.6134 20.3302C32.0222 19.6433 32.2015 19.253 32.3784 18.4524C32.4105 18.2286 32.428 18.002 32.428 17.7641ZM26.2381 21.3385C28.1798 21.3385 29.4236 19.7949 29.4236 17.4696C29.4236 15.5861 28.571 14.8893 27.3301 14.8893C26.4834 14.8893 26.1564 15.014 25.9345 16.2205L24.9768 21.126C25.2571 21.2337 25.7126 21.3385 26.2381 21.3385Z" fill="url(#paint0_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<rect width="20" height="4" rx="2" transform="matrix(-1 0 0 1 53 33)" fill="url(#paint1_linear_781_8517)"/>
<rect width="12.6667" height="4" rx="2" transform="matrix(-1 0 0 1 52.6666 42)" fill="url(#paint2_linear_781_8517)"/>
<g filter="url(#filter1_b_781_8517)">
<mask id="path-10-inside-1_781_8517" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.5845 56L18.1322 54.399C16.9148 55.3563 15.3938 55.9448 13.7375 55.9963C13.8188 55.9988 13.9005 56 13.9825 56H28.5845ZM20.5895 46.0465C19.5731 43.1092 16.7829 41 13.5 41C9.43737 41 6.1292 44.2302 6.00368 48.2624C6.00123 48.1811 6 48.0994 6 48.0175C6 43.2249 10.1936 39.5126 14.9509 40.094L37.1428 42.8064C41.2138 43.3039 43.5176 47.111 42.8565 50.6284L21.5 45.5001L20.5895 46.0465Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.5845 56L18.1322 54.399C16.9148 55.3563 15.3938 55.9448 13.7375 55.9963C13.8188 55.9988 13.9005 56 13.9825 56H28.5845ZM20.5895 46.0465C19.5731 43.1092 16.7829 41 13.5 41C9.43737 41 6.1292 44.2302 6.00368 48.2624C6.00123 48.1811 6 48.0994 6 48.0175C6 43.2249 10.1936 39.5126 14.9509 40.094L37.1428 42.8064C41.2138 43.3039 43.5176 47.111 42.8565 50.6284L21.5 45.5001L20.5895 46.0465Z" fill="#80A3FF" fill-opacity="0.5"/>
<path d="M28.5845 56V57L28.7359 55.0116L28.5845 56ZM18.1322 54.399L18.2836 53.4105L17.8549 53.3448L17.5141 53.6129L18.1322 54.399ZM13.7375 55.9963L13.7064 54.9968L13.7073 56.9959L13.7375 55.9963ZM20.5895 46.0465L19.6444 46.3735L20.0474 47.5379L21.104 46.904L20.5895 46.0465ZM6.00368 48.2624L5.00413 48.2925L7.0032 48.2935L6.00368 48.2624ZM14.9509 40.094L15.0722 39.1014L14.9509 40.094ZM37.1428 42.8064L37.0215 43.799L37.1428 42.8064ZM42.8565 50.6284L42.6231 51.6007L43.6452 51.8462L43.8393 50.8131L42.8565 50.6284ZM21.5 45.5001L21.7335 44.5278L21.336 44.4323L20.9855 44.6426L21.5 45.5001ZM28.7359 55.0116L18.2836 53.4105L17.9808 55.3874L28.4331 56.9885L28.7359 55.0116ZM17.5141 53.6129C16.4587 54.4427 15.142 54.9522 13.7064 54.9968L13.7686 56.9959C15.6456 56.9375 17.3709 56.2698 18.7503 55.185L17.5141 53.6129ZM13.7073 56.9959C13.7987 56.9986 13.8905 57 13.9825 57V55C13.9106 55 13.8389 54.9989 13.7676 54.9968L13.7073 56.9959ZM13.9825 57H28.5845V55H13.9825V57ZM13.5 42C16.3436 42 18.7632 43.8266 19.6444 46.3735L21.5345 45.7194C20.383 42.3919 17.2221 40 13.5 40V42ZM7.0032 48.2935C7.11196 44.7995 9.97923 42 13.5 42V40C8.89551 40 5.14643 43.6609 5.00417 48.2312L7.0032 48.2935ZM5 48.0175C5 48.1095 5.00138 48.2011 5.00413 48.2925L7.00323 48.2323C7.00108 48.161 7 48.0894 7 48.0175H5ZM15.0722 39.1014C9.71898 38.4471 5 42.6245 5 48.0175H7C7 43.8253 10.6683 40.578 14.8296 41.0866L15.0722 39.1014ZM37.2641 41.8138L15.0722 39.1014L14.8296 41.0866L37.0215 43.799L37.2641 41.8138ZM43.8393 50.8131C44.5968 46.7827 41.962 42.3879 37.2641 41.8138L37.0215 43.799C40.4656 44.2199 42.4384 47.4393 41.8737 50.4437L43.8393 50.8131ZM21.2665 46.4725L42.6231 51.6007L43.09 49.656L21.7335 44.5278L21.2665 46.4725ZM21.104 46.904L22.0145 46.3576L20.9855 44.6426L20.075 45.189L21.104 46.904Z" fill="url(#paint3_linear_781_8517)" mask="url(#path-10-inside-1_781_8517)"/>
</g>
<g filter="url(#filter2_b_781_8517)">
<mask id="path-12-inside-2_781_8517" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.8486 42.885C36.6943 44.2716 36 46.0547 36 48C36 52.4183 39.5817 56 44 56C44.2828 56 44.5622 55.9853 44.8374 55.9567C44.6819 55.9852 44.5216 56.0001 44.3579 56.0001H13.9836C9.57439 56.0001 6 52.4257 6 48.0165C6 43.224 10.1928 39.5114 14.9501 40.0916L37.7109 42.8673C37.7569 42.8729 37.8028 42.8788 37.8486 42.885Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.8486 42.885C36.6943 44.2716 36 46.0547 36 48C36 52.4183 39.5817 56 44 56C44.2828 56 44.5622 55.9853 44.8374 55.9567C44.6819 55.9852 44.5216 56.0001 44.3579 56.0001H13.9836C9.57439 56.0001 6 52.4257 6 48.0165C6 43.224 10.1928 39.5114 14.9501 40.0916L37.7109 42.8673C37.7569 42.8729 37.8028 42.8788 37.8486 42.885Z" fill="#80A3FF" fill-opacity="0.5"/>
<path d="M37.8486 42.885L38.6171 43.5248L39.7733 42.1359L37.9825 41.894L37.8486 42.885ZM44.8374 55.9567L45.0177 56.9403L44.7339 54.9621L44.8374 55.9567ZM14.9501 40.0916L14.829 41.0842L14.9501 40.0916ZM37.7109 42.8673L37.832 41.8746H37.832L37.7109 42.8673ZM37 48C37 46.297 37.6068 44.7384 38.6171 43.5248L37.08 42.2452C35.7817 43.8048 35 45.8124 35 48H37ZM44 55C40.134 55 37 51.866 37 48H35C35 52.9706 39.0294 57 44 57V55ZM44.7339 54.9621C44.493 54.9871 44.2482 55 44 55V57C44.3174 57 44.6313 56.9835 44.9409 56.9513L44.7339 54.9621ZM44.6571 54.9731C44.5608 54.9907 44.4609 55.0001 44.3579 55.0001V57.0001C44.5824 57.0001 44.803 56.9797 45.0177 56.9403L44.6571 54.9731ZM44.3579 55.0001H13.9836V57.0001H44.3579V55.0001ZM13.9836 55.0001C10.1267 55.0001 7 51.8734 7 48.0165H5C5 52.978 9.0221 57.0001 13.9836 57.0001V55.0001ZM7 48.0165C7 43.8243 10.6676 40.5767 14.829 41.0842L15.0711 39.0989C9.71798 38.4461 5 42.6237 5 48.0165H7ZM14.829 41.0842L37.5899 43.8599L37.832 41.8746L15.0711 39.0989L14.829 41.0842ZM37.5899 43.8599C37.6316 43.865 37.6732 43.8704 37.7147 43.876L37.9825 41.894C37.9324 41.8872 37.8823 41.8808 37.832 41.8746L37.5899 43.8599Z" fill="url(#paint4_linear_781_8517)" mask="url(#path-12-inside-2_781_8517)"/>
</g>
<rect x="-367.5" y="-19.5" width="623" height="101" rx="4.5" stroke="#9747FF" stroke-dasharray="10 5"/>
<defs>
<filter id="filter0_d_781_8517" x="11" y="8" width="33.1049" height="32.9072" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.317647 0 0 0 0 0.427451 0 0 0 0 1 0 0 0 0.49 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<filter id="filter1_b_781_8517" x="2" y="36.0339" width="44.9688" height="23.9661" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_781_8517" result="shape"/>
</filter>
<filter id="filter2_b_781_8517" x="2" y="36.0317" width="46.8374" height="23.9683" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_781_8517" result="shape"/>
</filter>
<linearGradient id="paint0_linear_781_8517" x1="21.5" y1="14.5" x2="38.0594" y2="23.3083" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.64"/>
</linearGradient>
<linearGradient id="paint1_linear_781_8517" x1="-0.5" y1="1.69231" x2="14.1887" y2="-8.18263" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.42"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_781_8517" x1="-0.316667" y1="1.69231" x2="11.1178" y2="-3.17623" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.42"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_781_8517" x1="29.9713" y1="36.2267" x2="43.8143" y2="56.3437" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.78"/>
<stop offset="1" stop-color="white" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint4_linear_781_8517" x1="12.2121" y1="40.2358" x2="22.8434" y2="57.2807" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9E8FF" stop-opacity="0.53"/>
<stop offset="0.497352" stop-color="white" stop-opacity="0.600833"/>
<stop offset="1" stop-color="white" stop-opacity="0.14"/>
</linearGradient>
</defs>
</svg>
