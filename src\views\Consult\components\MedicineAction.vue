<script setup lang="ts">
import { useConsultStore } from '@/stores'
import { showDialog, showToast } from 'vant'
import { computed, ref } from 'vue'
import MedicineCard from './MedicineCard.vue'
import { getCreateOrderParams } from '@/utils/createOrderParams'
import { ConsultType } from '@/enums'
import { useRouter } from 'vue-router'
import { createConsultOrder } from '@/services/consult'

withDefaults(
  defineProps<{
    from?: 'list' | 'detail'
  }>(),
  {
    from: 'list'
  }
)

//药品数量价格
const store = useConsultStore()
const totalPrice = computed(() => {
  return store.consult.medicines
    ?.reduce((sum, item) => {
      return (sum += +item.amount * +item.quantity)
    }, 0)
    .toFixed(2)
})
const totalCount = computed(() => {
  return store.consult.medicines?.reduce((sum, item) => {
    return (sum += +item.quantity)
  }, 0)
})

//控制支付抽屉
const show = ref(false)
// 底部操作栏注册事件，注意不是抽屉内容的底部操作栏
const openCart = () => {
  if (totalCount.value === 0) return showToast('请选择药品')
  show.value = true
}

const clear = () => {
  // console.log('clear')
  store.setMedicines([])
  show.value = false
}

//详情页加入药箱
const emit = defineEmits<{
  (e: 'addToCart'): void
}>()
const onAddToCart = () => {
  emit('addToCart')
}

// 跳转问诊室
const router = useRouter()
const onAskDocotor = async () => {
  const medicines = store.consult.medicines || []
  if (medicines?.length === 0) return showToast('请先选药')
  const params = getCreateOrderParams(store.consult, ConsultType.Medication)
  // console.log(params)
  try {
    const { data } = await createConsultOrder(params)
    router.push(`/room?orderId=${data.id}&from=medicine`)
  } catch (e) {
    return showDialog({
      title: '温馨提示',
      message: '问诊信息不完整请重新填写',
      closeOnPopstate: false
    }).then(() => {
      router.push('/')
    })
  } finally {
    store.clear()
  }
}
</script>

<template>
  <van-action-bar>
    <van-action-bar-icon
      @click="show = true"
      icon="cart-o"
      :badge="totalCount === 0 ? '' : totalCount"
      v-if="totalCount"
      :color="totalCount > 0 ? '#323233' : '#eee'"
    />
    <div class="total-price">￥ {{ totalPrice }}</div>
    <van-action-bar-button
      v-if="from === 'list'"
      type="primary"
      text="申请开方"
      @click="openCart"
    />
    <van-action-bar-button
      v-else
      type="primary"
      text="加入药箱"
      @click="onAddToCart"
    ></van-action-bar-button>
  </van-action-bar>
  <!-- 支付抽屉 -->
  <van-action-sheet v-model:show="show">
    <div class="content">
      <div class="content-header">
        <div class="content-header-left">
          <span>药品清单</span><span>共{{ totalCount }}件商品</span>
        </div>
        <div class="content-header-right" @click="clear">
          <van-icon name="delete-o" />
          <span>清空</span>
        </div>
      </div>
      <!-- 列表 -->
      <div style="padding-bottom: 45px">
        <medicine-card
          v-for="item in store.consult.medicines"
          :key="item.id"
          :item="item"
        ></medicine-card>
      </div>
    </div>

    <van-action-bar>
      <van-action-bar-icon
        icon="cart-o"
        v-if="totalCount"
        :color="totalCount > 0 ? '#323233' : '#eee'"
        :badge="totalCount === 0 ? '' : totalCount"
      />
      <div class="total-price">￥ {{ totalPrice }}</div>
      <van-action-bar-button
        type="primary"
        text="申请开方"
        @click="onAskDocotor"
      />
    </van-action-bar>
  </van-action-sheet>
</template>

<style lang="scss" scoped>
.van-action-bar {
  border-top: 1px solid rgba(237, 237, 237, 0.9);
  .total-price {
    width: 200px;
    font-size: 24px;
    line-height: 18px;
    font-weight: 700;
    color: #121826;
  }
}

.content {
  --content-height: 400px;
  --content-header-height: 25px;
  padding: 16px;
  height: var(--content-height);
  .content-header {
    position: sticky;
    top: 0px;
    z-index: 10;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--content-header-height);
    padding-bottom: 10px;
    &-left {
      span {
        font-size: 16px;
        color: #000000;
        margin-right: 10px;
      }
      span + span {
        font-size: 13px;
        color: var(--cp-primary);
      }
    }
    &-right {
      span {
        margin-left: 5px;
      }
    }
  }
  .medicine-list {
    padding-bottom: 45px;
  }
}
</style>
