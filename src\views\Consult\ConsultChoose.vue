<script setup lang="ts">
import { ref } from 'vue'
import MedicineList from './components/MedicineList.vue'
import MedicineAction from './components/MedicineAction.vue'

// 搜索药品
const searchValue = ref('')
const keyword = ref('')
const onSearch = () => {
  keyword.value = searchValue.value
}
const onCancel = () => {
  keyword.value = ''
}

//药品数量价格
// const store = useConsultStore()
// const totalPrice = computed(() => {
//   return store.consult.medicines
//     ?.reduce((sum, item) => {
//       return (sum += +item.amount * +item.quantity)
//     }, 0)
//     .toFixed(2)
// })
// const totalCount = computed(() => {
//   return store.consult.medicines?.reduce((sum, item) => {
//     return (sum += +item.quantity)
//   }, 0)
// })

//支付抽屉
// const show = ref(false)
// 底部操作栏注册事件，注意不是抽屉内容的底部操作栏
// const openCart = () => {
//   if (totalCount.value === 0) return showToast('请选择药品')
//   show.value = true
// }

// const clear = () => {
//   // console.log('clear')
//   store.setMedicines([])
//   show.value = false
// }
</script>

<template>
  <div class="consult-choose-page">
    <cp-nav-bar title="选择药品"></cp-nav-bar>
    <van-search
      v-model="searchValue"
      show-action
      placeholder="搜一搜: 药品名称"
      @search="onSearch"
      @cancel="onCancel"
    />
    <!-- 药品列表 -->
    <medicine-list :keyword="keyword"></medicine-list>
    <!-- 底部操作栏 -->
    <medicine-action></medicine-action>
  </div>
</template>

<style scoped lang="scss">
.consult-choose-page {
  padding-top: 46px;

  .van-stepper {
    position: absolute;
    right: 0;
    bottom: 15px;
    :deep() {
      .van-stepper__input {
        background: none;
      }
      .van-stepper__minus {
        background-color: #fff;
        border: 0.5px solid #16c2a3;
      }
      .van-stepper__plus {
        background-color: #eaf8f6;
      }
      .van-stepper__minus,
      .van-stepper__plus {
        width: 20px;
        height: 20px;
      }
    }
    &.hide {
      :deep() {
        .van-stepper__minus,
        .van-stepper__input {
          visibility: hidden;
        }
      }
    }
  }

  .van-search {
    position: sticky;
    top: 46px;
    z-index: 10;
    background-color: #fff;
  }
  .van-action-bar {
    border-top: 1px solid rgba(237, 237, 237, 0.9);
    .total-price {
      width: 200px;
      font-size: 24px;
      line-height: 18px;
      font-weight: 700;
      color: #121826;
    }
  }
  .medicine-list {
    background-color: #fff;
    padding: 0 15px 45px;
    .item {
      display: flex;
      flex-wrap: wrap;
      padding: 15px 0;
      .img {
        width: 80px;
        height: 70px;
        border-radius: 2px;
        overflow: hidden;
      }
      .info {
        padding-left: 15px;
        width: 250px;
        .name {
          display: flex;
          font-size: 15px;
          margin-bottom: 5px;
          > span:first-child {
            // width: 200px;
            // width: 300px;
            width: 40vw;
          }
          > span:last-child {
            // width: 50px;
            text-align: right;
          }
        }
        .size {
          margin-bottom: 5px;
          .van-tag {
            background-color: var(--cp-primary);
            vertical-align: middle;
          }
          span:not(.van-tag) {
            margin-left: 10px;
            color: var(--cp-tag);
            vertical-align: middle;
          }
        }
        .price {
          font-size: 16px;
          color: #eb5757;
        }
      }
      .desc {
        width: 100%;
        background-color: var(--cp-bg);
        border-radius: 4px;
        margin-top: 10px;
        padding: 4px 10px;
        color: var(--cp-tip);
      }
    }
  }
  .content {
    --content-height: 400px;
    --content-header-height: 25px;
    padding: 16px;
    height: var(--content-height);
    .content-header {
      position: sticky;
      top: 0px;
      z-index: 10;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: var(--content-header-height);
      padding-bottom: 10px;
      &-left {
        span {
          font-size: 16px;
          color: #000000;
          margin-right: 10px;
        }
        span + span {
          font-size: 13px;
          color: var(--cp-primary);
        }
      }
      &-right {
        span {
          margin-left: 5px;
        }
      }
    }
    .medicine-list {
      padding-bottom: 45px;
    }
  }
}
</style>
