<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="80" height="80" rx="40" fill="url(#paint0_linear_784_9534)"/>
<path d="M50.625 51L56.575 55.2C56.7534 55.3259 56.8852 55.5015 56.9517 55.7018C57.0182 55.9022 57.016 56.1171 56.9455 56.3162C56.875 56.5153 56.7397 56.6885 56.5588 56.8112C56.3778 56.934 56.1605 57 55.9375 57H40L50.625 51Z" fill="white" fill-opacity="0.85"/>
<g filter="url(#filter0_d_784_9534)">
<path d="M43.1875 23C50.8163 23 57 29.1838 57 36.8125V43.1875C57 50.8163 50.8163 57 43.1875 57H36.8125C29.1838 57 23 50.8163 23 43.1875V36.8125C23 29.1838 29.1838 23 36.8125 23H43.1875ZM31.5 37.875C30.9361 37.8753 30.3955 38.0995 29.997 38.4985C29.5984 38.8974 29.3747 39.4383 29.375 40.0021C29.3753 40.566 29.5995 41.1067 29.9985 41.5052C30.3974 41.9037 30.9383 42.1274 31.5021 42.1271C31.7813 42.127 32.0578 42.0719 32.3157 41.9649C32.5735 41.8579 32.8078 41.7012 33.0052 41.5037C33.2025 41.3061 33.359 41.0717 33.4657 40.8137C33.5724 40.5557 33.6273 40.2792 33.6271 40C33.627 39.7208 33.5719 39.4444 33.4649 39.1865C33.3579 38.9286 33.2012 38.6943 33.0037 38.497C32.8061 38.2996 32.5717 38.1431 32.3137 38.0364C32.0557 37.9297 31.7792 37.8749 31.5 37.875ZM40 37.875C39.4361 37.8753 38.8955 38.0995 38.497 38.4985C38.0984 38.8974 37.8747 39.4383 37.875 40.0021C37.8753 40.566 38.0995 41.1067 38.4985 41.5052C38.8974 41.9037 39.4383 42.1274 40.0021 42.1271C40.566 42.1268 41.1067 41.9026 41.5052 41.5037C41.9037 41.1048 42.1274 40.5639 42.1271 40C42.1268 39.4361 41.9026 38.8955 41.5037 38.497C41.1048 38.0984 40.5639 37.8747 40 37.875ZM48.5 37.875C47.9361 37.8753 47.3955 38.0995 46.997 38.4985C46.5984 38.8974 46.3747 39.4383 46.375 40.0021C46.3753 40.566 46.5995 41.1067 46.9985 41.5052C47.3974 41.9037 47.9383 42.1274 48.5021 42.1271C49.066 42.1268 49.6067 41.9026 50.0052 41.5037C50.4037 41.1048 50.6274 40.5639 50.6271 40C50.6268 39.4361 50.4026 38.8955 50.0037 38.497C49.6048 38.0984 49.0639 37.8747 48.5 37.875Z" fill="white"/>
</g>
<rect x="-253.5" y="-94.5" width="601" height="209" rx="19.5" stroke="black"/>
<rect x="-307.5" y="-183.5" width="2535" height="1373" rx="39.5" stroke="#EF5533"/>
<defs>
<filter id="filter0_d_784_9534" x="20" y="20" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.407843 0 0 0 0 0.678431 0 0 0 0 0.956863 0 0 0 0.3 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_784_9534"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_784_9534" result="shape"/>
</filter>
<linearGradient id="paint0_linear_784_9534" x1="19" y1="3.68804e-07" x2="40" y2="80" gradientUnits="userSpaceOnUse">
<stop stop-color="#88DFF2"/>
<stop offset="1" stop-color="#538AF4"/>
</linearGradient>
</defs>
</svg>
