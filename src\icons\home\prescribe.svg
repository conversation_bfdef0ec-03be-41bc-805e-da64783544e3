<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_781_8517)">
<ellipse cx="46" cy="75" rx="16" ry="6" fill="#FFA959" fill-opacity="0.82"/>
</g>
<path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M64.8382 28.6798C65.1911 28.8198 65.5694 28.8919 65.9515 28.8919C66.7226 28.8911 67.4619 28.5972 68.0069 28.0747C68.5518 27.5522 68.858 26.8439 68.858 26.1054V14.563C68.8557 12.5585 68.0235 10.6366 66.5438 9.21888C65.0642 7.80111 63.0578 7.00316 60.9648 7H30.6941C28.6005 7.00237 26.5933 7.79997 25.113 9.21783C23.6326 10.6357 22.8 12.558 22.7977 14.563V26.1054C22.7914 26.4751 22.862 26.8423 23.0054 27.1857C23.1487 27.529 23.362 27.8416 23.6328 28.1052C23.9036 28.3688 24.2264 28.5781 24.5825 28.721C24.9386 28.8639 25.3208 28.9375 25.7069 28.9375C26.093 28.9375 26.4753 28.8639 26.8314 28.721C27.1875 28.5781 27.5103 28.3688 27.7811 28.1052C28.0519 27.8416 28.2651 27.529 28.4085 27.1857C28.5519 26.8423 28.6224 26.4751 28.6161 26.1054V14.563C28.6167 14.0354 28.8359 13.5296 29.2254 13.1565C29.615 12.7834 30.1431 12.5736 30.6941 12.573H60.9648C61.5157 12.5736 62.0438 12.7834 62.4334 13.1565C62.823 13.5296 63.0421 14.0354 63.0427 14.563V26.1054C63.0426 26.4713 63.1178 26.8336 63.2639 27.1717C63.41 27.5098 63.6243 27.8169 63.8944 28.0757C64.1645 28.3345 64.4852 28.5397 64.8382 28.6798ZM74.2479 33.8126H17.7522C13.9578 33.8126 12.0607 35.6969 12.0607 39.4655V79.3472C12.0607 83.1158 13.9578 85.0001 17.7522 85.0001H74.2479C78.0423 85.0001 79.9395 83.1158 79.9395 79.3472V39.4655C79.9395 35.6969 78.0423 33.8126 74.2479 33.8126Z" fill="url(#paint0_linear_781_8517)"/>
<g filter="url(#filter1_bdii_781_8517)">
<path d="M6 32.4062C6 25.7788 11.3726 20.4062 18 20.4062H74C80.6274 20.4062 86 25.7788 86 32.4062V43.4062C86 52.2428 78.8366 59.4062 70 59.4062H22C13.1634 59.4062 6 52.2428 6 43.4062V32.4062Z" fill="#FAB02E" fill-opacity="0.15" shape-rendering="crispEdges"/>
<path d="M6.5 32.4062C6.5 26.055 11.6487 20.9062 18 20.9062H74C80.3513 20.9062 85.5 26.055 85.5 32.4062V43.4062C85.5 51.9667 78.5604 58.9062 70 58.9062H22C13.4396 58.9062 6.5 51.9667 6.5 43.4062V32.4062Z" stroke="url(#paint1_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_781_8517)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.3636 69.375C42.3636 69.9273 42.8114 70.375 43.3636 70.375H48.6364C49.1887 70.375 49.6364 69.9273 49.6364 69.375V63.0626H55.909C56.4613 63.0626 56.909 62.6149 56.909 62.0626V56.7501C56.909 56.1979 56.4613 55.7501 55.909 55.7501H49.6364V49.4375C49.6364 48.8852 49.1887 48.4375 48.6364 48.4375L43.3636 48.4375C42.8114 48.4375 42.3636 48.8852 42.3636 49.4375L42.3636 55.7501H36.0908C35.5385 55.7501 35.0908 56.1979 35.0908 56.7501V62.0626C35.0908 62.6149 35.5385 63.0626 36.0908 63.0626H42.3636L42.3636 69.375Z" fill="white"/>
</g>
<defs>
<filter id="filter0_f_781_8517" x="14" y="53" width="64" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_781_8517"/>
</filter>
<filter id="filter1_bdii_781_8517" x="-2" y="12.4062" width="98" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.818824 0 0 0 0 0.176471 0 0 0 0.15 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_781_8517" result="effect2_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_781_8517" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.736667 0 0 0 0 0.341667 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_781_8517"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="6.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_781_8517" result="effect4_innerShadow_781_8517"/>
</filter>
<filter id="filter2_d_781_8517" x="28.0908" y="42.4375" width="37.8182" height="37.9375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9875 0 0 0 0 0.910557 0 0 0 0 0.218073 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<linearGradient id="paint0_linear_781_8517" x1="26.4337" y1="8.21875" x2="64.3406" y2="84.8362" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB800"/>
<stop offset="1" stop-color="#FF7A00"/>
</linearGradient>
<linearGradient id="paint1_linear_781_8517" x1="12.6667" y1="25.2813" x2="61.9304" y2="59.1549" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.52"/>
<stop offset="1" stop-color="white" stop-opacity="0.14"/>
</linearGradient>
</defs>
</svg>
