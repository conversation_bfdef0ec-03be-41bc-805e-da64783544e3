<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_781_8517)">
<ellipse cx="46" cy="78" rx="16" ry="6" fill="#15CFCF" fill-opacity="0.5"/>
</g>
<g filter="url(#filter1_b_781_8517)">
<mask id="mask0_781_8517" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="7" y="44" width="78" height="42">
<path d="M7 61.4589L35.8859 47.3156C42.2667 44.1914 49.7333 44.1914 56.1141 47.3157L85 61.4589V85.9999H7V61.4589Z" fill="#25D9C3"/>
</mask>
<g mask="url(#mask0_781_8517)">
<path d="M7 68.9446C7 64.3633 9.60849 60.1817 13.7231 58.1671L33.6872 48.3922C41.4552 44.5888 50.5448 44.5888 58.3128 48.3922L78.2769 58.1671C82.3915 60.1817 85 64.3633 85 68.9446V79.9999C85 83.3136 82.3137 85.9999 79 85.9999H13C9.68629 85.9999 7 83.3136 7 79.9999V68.9446Z" fill="url(#paint0_linear_781_8517)"/>
<path d="M29.6544 28.7043H69.8732V44.711C69.8732 52.9953 63.1574 59.711 54.8732 59.711H44.6544C36.3701 59.711 29.6544 52.9953 29.6544 44.711V28.7043Z" fill="#0DB9B9"/>
</g>
<g filter="url(#filter2_bdii_781_8517)">
<path d="M26.5 22.9695H65.5V38.2725C65.5 46.5568 58.7843 53.2725 50.5 53.2725H41.5C33.2157 53.2725 26.5 46.5568 26.5 38.2725V22.9695Z" fill="#6DF0DF" fill-opacity="0.5" shape-rendering="crispEdges"/>
<path d="M27 23.4695H65V38.2725C65 46.2806 58.5081 52.7725 50.5 52.7725H41.5C33.4919 52.7725 27 46.2806 27 38.2725V23.4695Z" stroke="url(#paint1_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.5 6C30.0817 6 26.5 9.58172 26.5 14V20.5454H65.5V14C65.5 9.58172 61.9183 6 57.5 6H34.5Z" fill="url(#paint2_linear_781_8517)"/>
<g filter="url(#filter3_d_781_8517)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.5073 77.7272C28.5073 78.2795 28.955 78.7272 29.5073 78.7272H33.6011C34.1533 78.7272 34.6011 78.2795 34.6011 77.7272V72.6666H39.6948C40.2471 72.6666 40.6948 72.2189 40.6948 71.6666V67.606C40.6948 67.0537 40.2471 66.606 39.6948 66.606H34.6011V61.5454C34.6011 60.9931 34.1533 60.5454 33.6011 60.5454L29.5073 60.5454C28.955 60.5454 28.5073 60.9931 28.5073 61.5454L28.5073 66.606H23.4136C22.8613 66.606 22.4136 67.0537 22.4136 67.606V71.6666C22.4136 72.2189 22.8613 72.6666 23.4136 72.6666H28.5073L28.5073 77.7272Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_781_8517" x="14" y="56" width="64" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_781_8517"/>
</filter>
<filter id="filter1_b_781_8517" x="3" y="2" width="86" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_781_8517" result="shape"/>
</filter>
<filter id="filter2_bdii_781_8517" x="18.5" y="14.9695" width="57" height="49.303" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.72549 0 0 0 0 0.972549 0 0 0 0 0.941176 0 0 0 1 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_781_8517" result="effect2_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_781_8517" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.341667 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_781_8517"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="6.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_781_8517" result="effect4_innerShadow_781_8517"/>
</filter>
<filter id="filter3_d_781_8517" x="15.4136" y="54.5454" width="34.2812" height="34.1819" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0588235 0 0 0 0 0.648941 0 0 0 0 0.835294 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<linearGradient id="paint0_linear_781_8517" x1="23.0588" y1="42.5714" x2="80.2351" y2="91.3499" gradientUnits="userSpaceOnUse">
<stop stop-color="#22E7E7"/>
<stop offset="1" stop-color="#09C3CF"/>
</linearGradient>
<linearGradient id="paint1_linear_781_8517" x1="25.8906" y1="22.9695" x2="44.6328" y2="53.3641" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.59"/>
<stop offset="1" stop-color="white" stop-opacity="0.15"/>
</linearGradient>
<linearGradient id="paint2_linear_781_8517" x1="31.6618" y1="6" x2="64.2981" y2="23.8148" gradientUnits="userSpaceOnUse">
<stop stop-color="#21E5E6"/>
<stop offset="1" stop-color="#15CFCF"/>
</linearGradient>
</defs>
</svg>
