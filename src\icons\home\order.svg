<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 19.0222C5 16.6847 6.35749 14.56 8.47855 13.5777L24.4785 6.16774C26.0779 5.42704 27.9221 5.42704 29.5215 6.16774L45.5215 13.5777C47.6425 14.56 49 16.6847 49 19.0222V48.7305C49 53.0771 44.5222 55.9814 40.5534 54.209L29.4466 49.2489C27.8896 48.5536 26.1104 48.5536 24.5534 49.2489L13.4466 54.209C9.47781 55.9814 5 53.0771 5 48.7305V19.0222Z" fill="#4DB4FF"/>
<g filter="url(#filter0_d_781_8517)">
<rect x="10" y="22.3208" width="27" height="4.07547" rx="2.03774" fill="url(#paint0_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_781_8517)">
<rect x="10" y="31.4905" width="16" height="4.07547" rx="2.03774" fill="url(#paint1_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<mask id="mask0_781_8517" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="31" y="32" width="26" height="27">
<path d="M31 45.7548C31 53.0704 36.8199 59.0001 44 59.0001C51.1798 59.0001 57 53.0704 57 45.7548C57 38.4395 51.1798 32.5095 44 32.5095C36.8199 32.5095 31 38.4396 31 45.7548Z" fill="white"/>
</mask>
<g mask="url(#mask0_781_8517)">
<g filter="url(#filter2_bi_781_8517)">
<path d="M31 45.7548C31 53.0704 36.8199 59.0001 44 59.0001C51.1798 59.0001 57 53.0704 57 45.7548C57 38.4395 51.1798 32.5095 44 32.5095C36.8199 32.5095 31 38.4396 31 45.7548Z" fill="#00AAFF" fill-opacity="0.3"/>
<path d="M31 45.7548C31 53.0704 36.8199 59.0001 44 59.0001C51.1798 59.0001 57 53.0704 57 45.7548C57 38.4395 51.1798 32.5095 44 32.5095C36.8199 32.5095 31 38.4396 31 45.7548Z" stroke="url(#paint2_linear_781_8517)"/>
</g>
</g>
<g filter="url(#filter3_d_781_8517)">
<path d="M48.8894 40.7734C48.1489 40.0189 47.1734 39.6416 46.1979 39.6416C45.2224 39.6416 44.2469 40.0189 43.5064 40.7734L41.6863 42.6263L40.9294 43.3975L39.1108 45.2519C37.6297 46.7609 37.6297 49.229 39.1108 50.7365C39.8513 51.491 40.8268 51.8682 41.8023 51.8682C42.7778 51.8682 43.7532 51.491 44.4938 50.7365L46.3124 48.8835L47.0693 48.1124L48.8879 46.2594C50.3705 44.7504 50.3705 42.2824 48.8894 40.7734ZM43.7369 49.9653C43.2224 50.4895 42.5354 50.7774 41.8023 50.7774C41.0692 50.7774 40.3807 50.4895 39.8677 49.9653C39.3532 49.4411 39.0706 48.7411 39.0706 47.9942C39.0706 47.2472 39.3532 46.5458 39.8677 46.0231L41.6863 44.1701L45.5555 48.1124L43.7369 49.9653Z" fill="url(#paint3_linear_781_8517)" shape-rendering="crispEdges"/>
<path d="M43.8631 41.1237L43.8632 41.1236C44.5063 40.4685 45.3516 40.1416 46.1979 40.1416C47.0442 40.1416 47.8895 40.4685 48.5325 41.1236C49.8227 42.4381 49.8227 44.5945 48.5312 45.909L48.5311 45.9092L46.7124 47.7621L45.9556 48.5333L44.1369 50.3862C43.4939 51.0414 42.6486 51.3682 41.8023 51.3682C40.9559 51.3682 40.1106 51.0414 39.4676 50.3862L39.4675 50.386C38.1776 49.0732 38.1774 46.9168 39.4676 45.6021L39.4678 45.602L41.2863 43.7477L41.2864 43.7475L42.043 42.9767L42.0431 42.9765L43.8631 41.1237ZM47.0693 48.1124L48.8879 46.2594L39.1108 45.2519C37.6297 46.7609 37.6297 49.229 39.1108 50.7365C39.8513 51.491 40.8268 51.8682 41.8023 51.8682C42.7778 51.8682 43.7532 51.491 44.4938 50.7365L46.3124 48.8835L47.0693 48.1124ZM42.0431 43.8199L41.6863 43.4563L41.3295 43.8199L39.5114 45.6723C39.5113 45.6724 39.5113 45.6724 39.5112 45.6725C38.9023 46.2911 38.5706 47.1196 38.5706 47.9942C38.5706 48.8692 38.9025 49.6956 39.5107 50.3153C40.1182 50.936 40.9356 51.2774 41.8023 51.2774C42.6695 51.2774 43.4851 50.9356 44.0937 50.3155L45.9123 48.4626L46.2561 48.1124L45.9123 47.7621L42.0431 43.8199Z" stroke="url(#paint4_linear_781_8517)" shape-rendering="crispEdges"/>
</g>
<rect x="-19.5" y="-19.5" width="623" height="101" rx="4.5" stroke="#9747FF" stroke-dasharray="10 5"/>
<defs>
<filter id="filter0_d_781_8517" x="2" y="15.3208" width="43" height="20.0754" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.666667 0 0 0 0 1 0 0 0 0.73 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<filter id="filter1_d_781_8517" x="2" y="24.4905" width="32" height="20.0754" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.666667 0 0 0 0 1 0 0 0 0.73 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<filter id="filter2_bi_781_8517" x="29.5" y="31.0095" width="29" height="29.4905" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="0.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_781_8517" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_781_8517"/>
</filter>
<filter id="filter3_d_781_8517" x="30" y="32.6416" width="28" height="28.2266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.666667 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_781_8517"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_781_8517" result="shape"/>
</filter>
<linearGradient id="paint0_linear_781_8517" x1="10" y1="22.6343" x2="36.9153" y2="24.2242" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.61"/>
</linearGradient>
<linearGradient id="paint1_linear_781_8517" x1="10" y1="31.804" x2="25.9859" y2="32.3636" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.61"/>
</linearGradient>
<linearGradient id="paint2_linear_781_8517" x1="36.7197" y1="35.1586" x2="49.559" y2="57.7362" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.42"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_781_8517" x1="42.5" y1="41.6793" x2="47.6355" y2="49.744" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.52"/>
</linearGradient>
<linearGradient id="paint4_linear_781_8517" x1="38" y1="39.6416" x2="46.2028" y2="51.2148" gradientUnits="userSpaceOnUse">
<stop offset="0.3081" stop-color="white" stop-opacity="0.68"/>
<stop offset="1" stop-color="white" stop-opacity="0.28"/>
</linearGradient>
</defs>
</svg>
