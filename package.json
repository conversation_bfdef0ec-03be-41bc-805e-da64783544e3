{"name": "consult-patient-vue3", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vueuse/core": "^10.11.1", "axios": "^1.7.3", "dayjs": "^1.11.12", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "socket.io-client": "^4.7.5", "vant": "^4.8.4", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/node": "^20.11.10", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^9.0.10", "lint-staged": "^15.2.2", "npm-run-all2": "^6.1.1", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.0.3", "sass": "^1.70.0", "typescript": "~5.3.0", "unplugin-vue-components": "^0.27.4", "vite": "^5.0.11", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}}